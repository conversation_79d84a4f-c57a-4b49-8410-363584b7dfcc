// Hooks index file - Re-export all hooks

export { default as useError<PERSON>and<PERSON> } from './useErrorHandler';
export { useForm, FormConfigs } from './useForm';
export { useLoading, useMultipleLoading, LoadingConfigs } from './useLoading';

// Re-export types
export type { UseFormOptions, UseFormReturn } from './useForm';
export type { UseLoadingOptions, UseLoadingReturn } from './useLoading';
