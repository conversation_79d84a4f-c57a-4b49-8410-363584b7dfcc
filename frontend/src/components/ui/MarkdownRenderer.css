.markdown-renderer {
  color: var(--md-sys-color-on-surface);
  line-height: 1.7;
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
}

/* Headings */
.markdown-renderer h1 {
  font-family: var(--md-sys-typescale-display-small-font);
  font-size: var(--md-sys-typescale-display-small-size);
  font-weight: var(--md-sys-typescale-display-small-weight);
  line-height: var(--md-sys-typescale-display-small-line-height);
  color: var(--md-sys-color-on-surface);
  margin: 32px 0 16px 0;
}

.markdown-renderer h2 {
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: var(--md-sys-typescale-headline-medium-weight);
  line-height: var(--md-sys-typescale-headline-medium-line-height);
  color: var(--md-sys-color-on-surface);
  margin: 28px 0 14px 0;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  padding-bottom: 8px;
}

.markdown-renderer h3 {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  line-height: var(--md-sys-typescale-headline-small-line-height);
  color: var(--md-sys-color-on-surface);
  margin: 24px 0 12px 0;
}

.markdown-renderer h4,
.markdown-renderer h5,
.markdown-renderer h6 {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  font-weight: var(--md-sys-typescale-title-medium-weight);
  line-height: var(--md-sys-typescale-title-medium-line-height);
  color: var(--md-sys-color-on-surface);
  margin: 20px 0 10px 0;
}

/* Paragraphs */
.markdown-renderer p {
  margin: 16px 0;
  color: var(--md-sys-color-on-surface);
}

/* Lists */
.markdown-renderer ul,
.markdown-renderer ol {
  margin: 16px 0;
  padding-left: 24px;
}

.markdown-renderer li {
  margin: 8px 0;
  color: var(--md-sys-color-on-surface);
}

.markdown-renderer li::marker {
  color: var(--md-sys-color-primary);
}

/* Code */
.markdown-renderer .inline-code {
  background: var(--md-sys-color-surface-container-high);
  color: var(--md-sys-color-on-surface);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Roboto Mono', monospace;
  font-size: 0.9em;
}

.markdown-renderer .code-block {
  background: var(--md-sys-color-surface-container-highest);
  color: var(--md-sys-color-on-surface);
  padding: 16px;
  border-radius: 12px;
  overflow-x: auto;
  margin: 16px 0;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.markdown-renderer .code-block code {
  font-family: 'Roboto Mono', monospace;
  font-size: 0.9em;
  line-height: 1.5;
}

/* Blockquotes */
.markdown-renderer .markdown-blockquote {
  border-left: 4px solid var(--md-sys-color-primary);
  background: var(--md-sys-color-surface-container-low);
  margin: 16px 0;
  padding: 16px 20px;
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: var(--md-sys-color-on-surface-variant);
}

.markdown-renderer .markdown-blockquote p {
  margin: 0;
}

/* Images */
.markdown-renderer img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 16px 0;
  box-shadow: var(--md-sys-elevation-level1);
}

/* Links */
.markdown-renderer a {
  color: var(--md-sys-color-primary);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.markdown-renderer a:hover {
  border-bottom-color: var(--md-sys-color-primary);
}

/* Tables */
.markdown-renderer .table-container {
  overflow-x: auto;
  margin: 16px 0;
  border-radius: 8px;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.markdown-renderer .markdown-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--md-sys-color-surface-container-lowest);
}

.markdown-renderer .markdown-table th {
  background: var(--md-sys-color-surface-container);
  color: var(--md-sys-color-on-surface);
  font-weight: var(--md-sys-typescale-title-medium-weight);
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.markdown-renderer .markdown-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  color: var(--md-sys-color-on-surface);
}

.markdown-renderer .markdown-table tr:last-child td {
  border-bottom: none;
}

/* Horizontal Rule */
.markdown-renderer hr {
  border: none;
  height: 1px;
  background: var(--md-sys-color-outline-variant);
  margin: 32px 0;
}

/* Strong and Emphasis */
.markdown-renderer strong {
  font-weight: 600;
  color: var(--md-sys-color-on-surface);
}

.markdown-renderer em {
  font-style: italic;
  color: var(--md-sys-color-on-surface-variant);
}

/* Task Lists */
.markdown-renderer input[type="checkbox"] {
  margin-right: 8px;
  accent-color: var(--md-sys-color-primary);
}
