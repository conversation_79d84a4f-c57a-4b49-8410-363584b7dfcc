/* About Page Styles */
.about-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  min-height: calc(100vh - 200px);
}

/* Hero Section */
.about-hero {
  padding: 4rem 2rem;
  background: #141414;
  margin-bottom: 3rem;
  min-height: 70vh;
  display: flex;
  align-items: center;
}

.about-hero-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  align-items: center;
}

/* Left Side - Content */
.about-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Right Side - Photo */
.about-photo {
  display: flex;
  justify-content: center;
  align-items: center;
}

.photo-container {
  position: relative;
  width: 300px;
  height: 400px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.photo-container:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.3);
}

.profile-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.photo-container:hover .profile-photo {
  transform: scale(1.05);
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(var(--md-sys-color-primary-rgb), 0.1) 0%,
    rgba(var(--md-sys-color-secondary-rgb), 0.1) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.photo-container:hover .photo-overlay {
  opacity: 1;
}

.camera-icon {
  font-size: 48px !important;
  color: var(--md-sys-color-on-primary-container);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.about-title {
  color: var(--md-sys-color-on-primary-container);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.about-subtitle {
  color: var(--md-sys-color-on-secondary-container);
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.about-description {
  color: var(--md-sys-color-on-primary-container);
  line-height: 1.6;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.about-highlights {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.highlight-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(8px);
}

.highlight-item md-icon {
  font-size: 24px !important;
  color: var(--md-sys-color-on-primary-container);
  flex-shrink: 0;
}

.highlight-item span {
  color: var(--md-sys-color-on-primary-container);
  font-weight: 500;
  font-size: 1rem;
}

.about-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Sections */
.about-section {
  padding: 3rem 2rem;
  margin-bottom: 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
  font-weight: 500;
}

.section-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  opacity: 0.8;
}

/* Blog Info Grid */
.blog-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.blog-info-card {
  --md-elevated-card-container-color: var(--md-sys-color-surface-container-low);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.blog-info-card:hover {
  transform: translateY(-4px);
}

.card-content {
  padding: 2rem;
  text-align: center;
}

.card-icon {
  font-size: 48px !important;
  color: var(--md-sys-color-primary);
  margin-bottom: 1rem;
}

.card-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
}

.card-description {
  color: var(--md-sys-color-on-surface-variant);
  line-height: 1.5;
}

/* Skills Simple */
.skills-simple {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.skill-tag {
  padding: 0.5rem 1rem;
  background: var(--md-sys-color-surface-container-low);
  border-radius: 16px;
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Experience Timeline */
.experience-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.experience-timeline::before {
  content: "";
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--md-sys-color-outline-variant);
}

.timeline-item {
  position: relative;
  padding-left: 80px;
  margin-bottom: 3rem;
}

.timeline-marker {
  position: absolute;
  left: 14px;
  top: 0;
  width: 32px;
  height: 32px;
  background: var(--md-sys-color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 0 4px var(--md-sys-color-surface);
}

.timeline-marker md-icon {
  color: var(--md-sys-color-on-primary);
  font-size: 18px !important;
}

.timeline-content {
  background: var(--md-sys-color-surface-container-lowest);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.timeline-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 0.5rem;
}

.timeline-company {
  color: var(--md-sys-color-primary);
  margin-bottom: 0.5rem;
}

.timeline-period {
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 1rem;
}

.timeline-description {
  color: var(--md-sys-color-on-surface-variant);
  line-height: 1.5;
}

/* Achievements Grid */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.achievement-item {
  text-align: center;
  padding: 2rem 1rem;
}

.achievement-icon {
  font-size: 64px !important;
  color: var(--md-sys-color-primary);
  margin-bottom: 1rem;
}

.achievement-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.achievement-description {
  color: var(--md-sys-color-on-surface-variant);
}

/* CTA Section */
.about-cta {
  background: var(--md-sys-color-surface-container-low);
  padding: 4rem 2rem;
  text-align: center;
  margin-top: 3rem;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
}

.cta-description {
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .about-hero-content {
    grid-template-columns: 1fr 300px;
    gap: 3rem;
  }

  .photo-container {
    width: 250px;
    height: 350px;
  }
}

@media (max-width: 768px) {
  .about-hero {
    padding: 2rem 1rem;
    min-height: auto;
  }

  .about-hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .about-content {
    order: 2;
  }

  .about-photo {
    order: 1;
  }

  .photo-container {
    width: 200px;
    height: 280px;
  }

  .about-highlights {
    align-items: center;
  }

  .highlight-item {
    max-width: 300px;
    justify-content: center;
  }

  .about-section {
    padding: 2rem 1rem;
  }

  .blog-info-grid {
    grid-template-columns: 1fr;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .achievements-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .timeline-item {
    padding-left: 60px;
  }

  .experience-timeline::before {
    left: 20px;
  }

  .timeline-marker {
    left: 4px;
  }
}

@media (max-width: 480px) {
  .about-hero {
    padding: 1.5rem 1rem;
  }

  .photo-container {
    width: 180px;
    height: 240px;
  }

  .about-title {
    font-size: 2rem;
  }

  .about-subtitle {
    font-size: 1.2rem;
  }

  .highlight-item {
    padding: 0.75rem;
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .highlight-item md-icon {
    font-size: 20px !important;
  }
}
