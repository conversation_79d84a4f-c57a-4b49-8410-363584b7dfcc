/* Contact Page Styles */
.contact-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  min-height: calc(100vh - 200px);
}

/* Hero Section */
.contact-hero {
  padding: 4rem 2rem;
  background: linear-gradient(
    135deg,
    var(--md-sys-color-tertiary-container) 0%,
    var(--md-sys-color-primary-container) 100%
  );
  text-align: center;
  margin-bottom: 3rem;
}

.contact-hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.contact-title {
  color: var(--md-sys-color-on-tertiary-container);
  margin-bottom: 1rem;
  font-weight: 600;
}

.contact-subtitle {
  color: var(--md-sys-color-on-primary-container);
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.contact-description {
  color: var(--md-sys-color-on-tertiary-container);
  line-height: 1.6;
  opacity: 0.8;
}

/* Content */
.contact-content {
  padding: 0 2rem;
}

/* Sections */
.section-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
  font-weight: 500;
  text-align: center;
}

.section-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  text-align: center;
  margin-bottom: 2rem;
  opacity: 0.8;
}

/* Contact Info */
.contact-info {
  margin-bottom: 3rem;
}

.contact-details {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
}

.contact-details p {
  margin-bottom: 1rem;
  color: var(--md-sys-color-on-surface);
}

.contact-details a {
  color: var(--md-sys-color-primary);
  text-decoration: none;
}

.contact-details a:hover {
  text-decoration: underline;
}

/* Contact Form */
.contact-form-section {
  margin-bottom: 4rem;
}

.contact-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--md-sys-color-surface-container-lowest);
  border-radius: 24px;
  border: 1px solid var(--md-sys-color-outline-variant);
}

/* Removed form-row - simplified layout */

.form-field {
  margin-bottom: 1rem;
  width: 100%;
}

.full-width {
  grid-column: 1 / -1;
}

.form-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 12px;
  margin-bottom: 1rem;
  font-size: 14px;
}

.form-message.success {
  background: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
}

.form-message.error {
  background: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
}

.form-message md-icon {
  font-size: 20px !important;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.submit-button {
  min-width: 160px;
}

/* Social Links */
.social-section {
  margin-bottom: 4rem;
}

.social-links-simple {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* FAQ Section */
.faq-section {
  margin-bottom: 4rem;
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  padding: 2rem;
  background: var(--md-sys-color-surface-container-lowest);
  border-radius: 16px;
  border: 1px solid var(--md-sys-color-outline-variant);
  margin-bottom: 1rem;
}

.faq-question {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
}

.faq-answer {
  color: var(--md-sys-color-on-surface-variant);
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-hero {
    padding: 2rem 1rem;
  }

  .contact-content {
    padding: 0 1rem;
  }

  .methods-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .social-links {
    grid-template-columns: repeat(2, 1fr);
  }

  .social-content {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .contact-hero {
    padding: 1.5rem 0.5rem;
  }

  .contact-content {
    padding: 0 0.5rem;
  }

  .contact-form {
    padding: 1rem;
  }

  .social-links {
    grid-template-columns: 1fr;
  }

  .faq-item {
    padding: 1.5rem;
  }
}

/* Animation */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.contact-methods,
.contact-form-section,
.social-section,
.faq-section {
  animation: slideInUp 0.6s ease-out;
}

.contact-methods {
  animation-delay: 0.1s;
}
.contact-form-section {
  animation-delay: 0.2s;
}
.social-section {
  animation-delay: 0.3s;
}
.faq-section {
  animation-delay: 0.4s;
}

/* Remove extra outline on focus */
.form-field:focus-within {
  outline: none;
}

/* Remove outline from Material Design components */
md-outlined-text-field:focus,
md-outlined-text-field:focus-within,
md-filled-text-field:focus,
md-filled-text-field:focus-within {
  outline: none !important;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .method-card,
  .social-card,
  .faq-item {
    border: 2px solid var(--md-sys-color-outline);
  }

  .contact-form {
    border: 2px solid var(--md-sys-color-outline);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .contact-methods,
  .contact-form-section,
  .social-section,
  .faq-section,
  .method-card,
  .social-card {
    animation: none;
    transition: none;
  }

  .method-card:hover,
  .social-card:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .contact-hero {
    background: none;
    color: black;
  }

  .contact-form,
  .social-links {
    display: none;
  }

  .methods-grid {
    display: block;
  }

  .method-card {
    break-inside: avoid;
    margin-bottom: 1rem;
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
