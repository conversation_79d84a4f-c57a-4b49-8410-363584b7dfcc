/* Tags Page Styles */
.tags-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  min-height: calc(100vh - 200px);
}

/* Page Header */
.tags-header {
  text-align: center;
  margin-bottom: 3rem;
}

.tags-header-content {
  max-width: 600px;
  margin: 0 auto;
}

.tags-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
  font-weight: 500;
}

.tags-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  opacity: 0.8;
}

/* Loading and Error States */
.tags-loading,
.articles-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.tags-error {
  text-align: center;
  padding: 3rem 1rem;
  max-width: 500px;
  margin: 0 auto;
}

.tags-error .error-icon {
  font-size: 4rem;
  color: var(--md-sys-color-error);
  margin-bottom: 1rem;
}

.tags-error h2 {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
}

.tags-error p {
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 2rem;
}

/* Controls Section */
.tags-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.tags-search {
  flex: 1;
  max-width: 400px;
}

.tags-search-field {
  width: 100%;
}

.tags-stats {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.stats-chip {
  --md-assist-chip-container-color: var(--md-sys-color-secondary-container);
  --md-assist-chip-label-text-color: var(--md-sys-color-on-secondary-container);
}

.selected-chip {
  --md-assist-chip-container-color: var(--md-sys-color-primary-container);
  --md-assist-chip-label-text-color: var(--md-sys-color-on-primary-container);
}

/* Sections */
.tags-section,
.articles-section {
  margin-bottom: 3rem;
}

.section-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

/* Tags Cloud */
.tags-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
  justify-content: flex-start;
  padding: 1rem;
  background: var(--md-sys-color-surface-container-lowest);
  border-radius: 16px;
  min-height: 120px;
}

.tag-chip {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --md-filter-chip-container-color: var(--md-sys-color-surface-container);
  --md-filter-chip-label-text-color: var(--md-sys-color-on-surface);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.tag-chip:hover {
  --md-filter-chip-container-color: var(--md-sys-color-surface-container-high);
  transform: translateY(-1px);
}

.tag-chip.selected {
  --md-filter-chip-container-color: var(--md-sys-color-primary-container);
  --md-filter-chip-label-text-color: var(--md-sys-color-on-primary-container);
  border-color: var(--md-sys-color-primary);
}

.tag-count {
  opacity: 0.7;
  font-size: 0.85em;
  margin-left: 0.25rem;
}

/* Tag Size Classes */
.tag-size-xs {
  font-size: 0.75rem;
  --md-filter-chip-container-height: 28px;
}

.tag-size-sm {
  font-size: 0.875rem;
  --md-filter-chip-container-height: 32px;
}

.tag-size-md {
  font-size: 1rem;
  --md-filter-chip-container-height: 36px;
}

.tag-size-lg {
  font-size: 1.125rem;
  --md-filter-chip-container-height: 40px;
  font-weight: 500;
}

.tag-size-xl {
  font-size: 1.25rem;
  --md-filter-chip-container-height: 44px;
  font-weight: 600;
}

/* No Tags State */
.no-tags {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--md-sys-color-on-surface-variant);
}

.no-tags-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

/* Articles Section */
.articles-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.clear-selection-btn {
  --md-text-button-label-text-color: var(--md-sys-color-primary);
}

/* Articles Grid */
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.tag-article-card {
  height: 100%;
}

/* No Articles State */
.no-articles {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--md-sys-color-on-surface-variant);
}

.no-articles-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.no-articles h3 {
  margin-bottom: 0.5rem;
  color: var(--md-sys-color-on-surface);
}

/* Help Section */
.tags-help {
  margin-top: 3rem;
  padding: 2rem;
  background: var(--md-sys-color-surface-container-lowest);
  border-radius: 16px;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.help-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.help-icon {
  color: var(--md-sys-color-primary);
  font-size: 24px;
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.help-text h3 {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 0.5rem;
}

.help-text p {
  color: var(--md-sys-color-on-surface-variant);
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tags-page {
    padding: 1rem 0.5rem;
  }

  .tags-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .tags-search {
    max-width: none;
  }

  .tags-stats {
    justify-content: center;
  }

  .articles-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .articles-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .tags-header {
    margin-bottom: 2rem;
  }

  .tags-title {
    font-size: 2rem;
  }

  .help-content {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .tags-page {
    padding: 0.5rem;
  }

  .tags-cloud {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .tag-chip {
    font-size: 0.875rem;
  }

  .tag-size-xs { font-size: 0.75rem; }
  .tag-size-sm { font-size: 0.8rem; }
  .tag-size-md { font-size: 0.875rem; }
  .tag-size-lg { font-size: 1rem; }
  .tag-size-xl { font-size: 1.125rem; }
}

/* Animation for tag selection */
@keyframes tagSelect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.tag-chip.selected {
  animation: tagSelect 0.3s ease-out;
}

/* Focus styles for accessibility */
.tag-chip:focus-visible {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}
