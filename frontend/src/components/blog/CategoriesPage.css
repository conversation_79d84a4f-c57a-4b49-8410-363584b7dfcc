/* Categories Page Styles */
.categories-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  min-height: calc(100vh - 200px);
}

/* Page Header */
.categories-header {
  text-align: center;
  margin-bottom: 3rem;
}

.categories-header-content {
  max-width: 600px;
  margin: 0 auto;
}

.categories-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
  font-weight: 500;
}

.categories-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  opacity: 0.8;
}

/* Loading and Error States */
.categories-loading,
.articles-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.categories-error {
  text-align: center;
  padding: 3rem 1rem;
  max-width: 500px;
  margin: 0 auto;
}

.categories-error .error-icon {
  font-size: 4rem;
  color: var(--md-sys-color-error);
  margin-bottom: 1rem;
}

.categories-error h2 {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
}

.categories-error p {
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 2rem;
}

/* Sections */
.categories-section,
.articles-section {
  margin-bottom: 3rem;
}

.section-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

/* Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.category-card {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  --md-elevated-card-container-color: var(--md-sys-color-surface-container-low);
}

.category-card:hover {
  --md-elevated-card-container-color: var(--md-sys-color-surface-container);
  transform: translateY(-2px);
}

.category-card.selected {
  border-color: var(--md-sys-color-primary);
  --md-elevated-card-container-color: var(--md-sys-color-primary-container);
}

.category-card-content {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.category-icon-container {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--md-sys-color-secondary-container);
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-card.selected .category-icon-container {
  background: var(--md-sys-color-primary);
}

.category-icon {
  font-size: 24px;
  color: var(--md-sys-color-on-secondary-container);
}

.category-card.selected .category-icon {
  color: var(--md-sys-color-on-primary);
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-name {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.category-card.selected .category-name {
  color: var(--md-sys-color-on-primary-container);
}

.category-count {
  color: var(--md-sys-color-on-surface-variant);
}

.category-card.selected .category-count {
  color: var(--md-sys-color-on-primary-container);
  opacity: 0.8;
}

.selected-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: var(--md-sys-color-primary);
  font-size: 20px;
}

/* Articles Section */
.articles-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.category-chip {
  --md-assist-chip-container-color: var(--md-sys-color-secondary-container);
  --md-assist-chip-label-text-color: var(--md-sys-color-on-secondary-container);
}

/* Articles Grid */
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.category-article-card {
  height: 100%;
}

/* No Articles State */
.no-articles {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--md-sys-color-on-surface-variant);
}

.no-articles-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.no-articles h3 {
  margin-bottom: 0.5rem;
  color: var(--md-sys-color-on-surface);
}

/* Responsive Design */
@media (max-width: 768px) {
  .categories-page {
    padding: 1rem 0.5rem;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .articles-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .articles-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .category-card-content {
    padding: 1rem;
  }

  .categories-header {
    margin-bottom: 2rem;
  }

  .categories-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .categories-page {
    padding: 0.5rem;
  }

  .category-card-content {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .category-icon-container {
    width: 40px;
    height: 40px;
  }

  .category-icon {
    font-size: 20px;
  }
}

/* Animation for category selection */
@keyframes categorySelect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.category-card.selected {
  animation: categorySelect 0.3s ease-out;
}

/* Focus styles for accessibility */
.category-card:focus-visible {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}
