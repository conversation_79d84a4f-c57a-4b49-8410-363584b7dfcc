/* Search Results Page Styles */
.search-results-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  min-height: calc(100vh - 200px);
}

/* Search Header */
.search-header {
  text-align: center;
  margin-bottom: 3rem;
}

.search-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 2rem;
  font-weight: 500;
}

/* Search Form */
.search-form {
  display: flex;
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto 1rem;
  align-items: flex-end;
}

.search-input {
  flex: 1;
}

/* Filters Toggle */
.filters-toggle {
  margin-top: 1rem;
}

/* Advanced Filters */
.advanced-filters {
  background: var(--md-sys-color-surface-container-lowest);
  border-radius: 16px;
  border: 1px solid var(--md-sys-color-outline-variant);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.filters-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-label {
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-options md-filter-chip {
  --md-filter-chip-container-color: var(--md-sys-color-surface-container);
  --md-filter-chip-selected-container-color: var(--md-sys-color-primary-container);
  --md-filter-chip-label-text-color: var(--md-sys-color-on-surface);
  --md-filter-chip-selected-label-text-color: var(--md-sys-color-on-primary-container);
}

/* Search Results */
.search-results {
  margin-top: 2rem;
}

.results-info {
  margin-bottom: 1.5rem;
  text-align: center;
}

.results-count {
  color: var(--md-sys-color-on-surface-variant);
  font-weight: 500;
}

/* Results Grid */
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.search-result-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.search-article-card {
  flex: 1;
  margin-bottom: 1rem;
}

.search-snippet {
  padding: 1rem;
  background: var(--md-sys-color-surface-container-lowest);
  border-radius: 12px;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.snippet-text {
  color: var(--md-sys-color-on-surface-variant);
  line-height: 1.5;
  margin: 0;
}

/* Search Highlight */
.search-highlight {
  background: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-weight: 500;
}

/* Loading and Error States */
.search-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.search-error {
  text-align: center;
  padding: 3rem 1rem;
  max-width: 500px;
  margin: 0 auto;
}

.search-error .error-icon {
  font-size: 4rem;
  color: var(--md-sys-color-error);
  margin-bottom: 1rem;
}

.search-error h2 {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
}

.search-error p {
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 2rem;
}

/* No Results State */
.no-results {
  text-align: center;
  padding: 4rem 1rem;
  color: var(--md-sys-color-on-surface-variant);
}

.no-results-icon {
  font-size: 5rem;
  margin-bottom: 1.5rem;
  opacity: 0.6;
  color: var(--md-sys-color-on-surface-variant);
}

.no-results h3 {
  margin-bottom: 1rem;
  color: var(--md-sys-color-on-surface);
}

.no-results p {
  max-width: 500px;
  margin: 0 auto 2rem;
  line-height: 1.6;
}

.no-results md-text-button {
  --md-text-button-label-text-color: var(--md-sys-color-primary);
}

/* Search Prompt */
.search-prompt {
  text-align: center;
  padding: 4rem 1rem;
  color: var(--md-sys-color-on-surface-variant);
}

.search-prompt-icon {
  font-size: 5rem;
  margin-bottom: 1.5rem;
  opacity: 0.6;
  color: var(--md-sys-color-primary);
}

.search-prompt h3 {
  margin-bottom: 1rem;
  color: var(--md-sys-color-on-surface);
}

.search-prompt p {
  max-width: 500px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-results-page {
    padding: 1rem 0.5rem;
  }

  .search-header {
    margin-bottom: 2rem;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .advanced-filters {
    padding: 1rem;
  }

  .filters-grid {
    gap: 1rem;
  }

  .filter-options {
    justify-content: flex-start;
  }

  .results-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .search-title {
    font-size: 2rem;
  }

  .no-results,
  .search-prompt {
    padding: 3rem 1rem;
  }

  .no-results-icon,
  .search-prompt-icon {
    font-size: 4rem;
  }
}

@media (max-width: 480px) {
  .search-results-page {
    padding: 0.5rem;
  }

  .advanced-filters {
    padding: 0.75rem;
  }

  .filters-grid {
    gap: 0.75rem;
  }

  .filter-options {
    gap: 0.25rem;
  }

  .results-grid {
    gap: 1rem;
  }

  .no-results,
  .search-prompt {
    padding: 2rem 0.5rem;
  }
}

/* Desktop Layout Optimization */
@media (min-width: 1024px) {
  .filters-grid {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }

  .filter-group {
    flex: 1;
    min-width: 200px;
  }

  .filter-group:not(:last-child) {
    margin-right: 2rem;
  }
}

/* Animation for search results */
@keyframes searchResultsFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.results-grid .search-result-card {
  animation: searchResultsFadeIn 0.4s ease forwards;
}

.results-grid .search-result-card:nth-child(1) { animation-delay: 0.1s; }
.results-grid .search-result-card:nth-child(2) { animation-delay: 0.15s; }
.results-grid .search-result-card:nth-child(3) { animation-delay: 0.2s; }
.results-grid .search-result-card:nth-child(4) { animation-delay: 0.25s; }
.results-grid .search-result-card:nth-child(5) { animation-delay: 0.3s; }
.results-grid .search-result-card:nth-child(6) { animation-delay: 0.35s; }

/* Focus styles for accessibility */
.search-input:focus-within {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .search-result-card {
    border: 1px solid var(--md-sys-color-outline);
  }
  
  .advanced-filters,
  .search-snippet {
    border: 2px solid var(--md-sys-color-outline);
  }
  
  .search-highlight {
    border: 1px solid var(--md-sys-color-primary);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .search-result-card,
  .results-grid .search-result-card {
    animation: none;
    transition: none;
  }
}

/* Print styles */
@media print {
  .search-form,
  .filters-toggle,
  .advanced-filters,
  .results-info {
    display: none;
  }
  
  .results-grid {
    display: block;
  }
  
  .search-result-card {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
  
  .search-highlight {
    background: #ffff00;
    color: #000;
  }
}
