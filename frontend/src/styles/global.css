/* Global Styles for Material Design 3 Blog */

/* Reset and Base Styles - Safe box-sizing that doesn't affect Material Design Web Components */
html,
body,
div,
span,
h1,
h2,
h3,
h4,
h5,
h6,
p,
a,
img,
ul,
ol,
li,
form,
input,
textarea {
  box-sizing: border-box;
}

/* Material Icons Support */
.material-icons,
.material-symbols-outlined {
  font-family: "Material Icons", "Material Symbols Outlined", sans-serif;
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
  font-feature-settings: "liga";
}

/* Material Web Components Icon Fixes */
md-icon {
  font-family: "Material Symbols Outlined", "Material Icons", sans-serif !important;
  font-weight: normal !important;
  font-style: normal !important;
  font-size: 24px !important;
  line-height: 1 !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  display: inline-block !important;
  white-space: nowrap !important;
  word-wrap: normal !important;
  direction: ltr !important;
  -webkit-font-feature-settings: "liga" !important;
  -webkit-font-smoothing: antialiased !important;
  font-feature-settings: "liga" !important;
  font-variation-settings: "FILL" 0, "wght" 400, "GRAD" 0, "opsz" 24 !important;
}

html {
  height: 100%;
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  line-height: var(--md-sys-typescale-body-medium-line-height);
  color: var(--md-sys-color-on-background);
  background-color: var(--md-sys-color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--md-sys-color-background);
  color: var(--md-sys-color-on-background);
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Typography Classes */
.md-typescale-display-large {
  font-family: var(--md-sys-typescale-display-large-font);
  font-weight: var(--md-sys-typescale-display-large-weight);
  font-size: var(--md-sys-typescale-display-large-size);
  line-height: var(--md-sys-typescale-display-large-line-height);
  letter-spacing: -0.25px;
}

.md-typescale-display-medium {
  font-family: var(--md-sys-typescale-display-medium-font);
  font-weight: var(--md-sys-typescale-display-medium-weight);
  font-size: var(--md-sys-typescale-display-medium-size);
  line-height: var(--md-sys-typescale-display-medium-line-height);
  letter-spacing: 0px;
}

.md-typescale-display-small {
  font-family: var(--md-sys-typescale-display-small-font);
  font-weight: var(--md-sys-typescale-display-small-weight);
  font-size: var(--md-sys-typescale-display-small-size);
  line-height: var(--md-sys-typescale-display-small-line-height);
  letter-spacing: 0px;
}

.md-typescale-headline-large {
  font-family: var(--md-sys-typescale-headline-large-font);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  font-size: var(--md-sys-typescale-headline-large-size);
  line-height: var(--md-sys-typescale-headline-large-line-height);
  letter-spacing: 0px;
}

.md-typescale-headline-medium {
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-weight: var(--md-sys-typescale-headline-medium-weight);
  font-size: var(--md-sys-typescale-headline-medium-size);
  line-height: var(--md-sys-typescale-headline-medium-line-height);
  letter-spacing: 0px;
}

.md-typescale-headline-small {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  font-size: var(--md-sys-typescale-headline-small-size);
  line-height: var(--md-sys-typescale-headline-small-line-height);
  letter-spacing: 0px;
}

.md-typescale-title-large {
  font-family: var(--md-sys-typescale-title-large-font);
  font-weight: var(--md-sys-typescale-title-large-weight);
  font-size: var(--md-sys-typescale-title-large-size);
  line-height: var(--md-sys-typescale-title-large-line-height);
  letter-spacing: 0px;
}

.md-typescale-title-medium {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-weight: var(--md-sys-typescale-title-medium-weight);
  font-size: var(--md-sys-typescale-title-medium-size);
  line-height: var(--md-sys-typescale-title-medium-line-height);
  letter-spacing: 0.15px;
}

.md-typescale-title-small {
  font-family: var(--md-sys-typescale-title-small-font);
  font-weight: var(--md-sys-typescale-title-small-weight);
  font-size: var(--md-sys-typescale-title-small-size);
  line-height: var(--md-sys-typescale-title-small-line-height);
  letter-spacing: 0.1px;
}

.md-typescale-body-large {
  font-family: var(--md-sys-typescale-body-large-font);
  font-weight: var(--md-sys-typescale-body-large-weight);
  font-size: var(--md-sys-typescale-body-large-size);
  line-height: var(--md-sys-typescale-body-large-line-height);
  letter-spacing: 0.5px;
}

.md-typescale-body-medium {
  font-family: var(--md-sys-typescale-body-medium-font);
  font-weight: var(--md-sys-typescale-body-medium-weight);
  font-size: var(--md-sys-typescale-body-medium-size);
  line-height: var(--md-sys-typescale-body-medium-line-height);
  letter-spacing: 0.25px;
}

.md-typescale-body-small {
  font-family: var(--md-sys-typescale-body-small-font);
  font-weight: var(--md-sys-typescale-body-small-weight);
  font-size: var(--md-sys-typescale-body-small-size);
  line-height: var(--md-sys-typescale-body-small-line-height);
  letter-spacing: 0.4px;
}

.md-typescale-label-large {
  font-family: var(--md-sys-typescale-label-large-font);
  font-weight: var(--md-sys-typescale-label-large-weight);
  font-size: var(--md-sys-typescale-label-large-size);
  line-height: var(--md-sys-typescale-label-large-line-height);
  letter-spacing: 0.1px;
}

.md-typescale-label-medium {
  font-family: var(--md-sys-typescale-label-medium-font);
  font-weight: var(--md-sys-typescale-label-medium-weight);
  font-size: var(--md-sys-typescale-label-medium-size);
  line-height: var(--md-sys-typescale-label-medium-line-height);
  letter-spacing: 0.5px;
}

.md-typescale-label-small {
  font-family: var(--md-sys-typescale-label-small-font);
  font-weight: var(--md-sys-typescale-label-small-weight);
  font-size: var(--md-sys-typescale-label-small-size);
  line-height: var(--md-sys-typescale-label-small-line-height);
  letter-spacing: 0.5px;
}

/* Layout Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--md-sys-spacing-4);
}

.container-compact {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--md-sys-spacing-4);
}

.container-medium {
  max-width: 840px;
  margin: 0 auto;
  padding: 0 var(--md-sys-spacing-4);
}

.container-expanded {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--md-sys-spacing-4);
}

/* Surface Utilities */
.surface {
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
}

.surface-variant {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
}

.surface-container {
  background-color: var(--md-sys-color-surface-container);
  color: var(--md-sys-color-on-surface);
}

.surface-container-low {
  background-color: var(--md-sys-color-surface-container-low);
  color: var(--md-sys-color-on-surface);
}

.surface-container-high {
  background-color: var(--md-sys-color-surface-container-high);
  color: var(--md-sys-color-on-surface);
}

.surface-container-highest {
  background-color: var(--md-sys-color-surface-container-highest);
  color: var(--md-sys-color-on-surface);
}

/* Elevation Utilities */
.elevation-0 {
  box-shadow: var(--md-sys-elevation-level0);
}

.elevation-1 {
  box-shadow: var(--md-sys-elevation-level1);
}

.elevation-2 {
  box-shadow: var(--md-sys-elevation-level2);
}

.elevation-3 {
  box-shadow: var(--md-sys-elevation-level3);
}

.elevation-4 {
  box-shadow: var(--md-sys-elevation-level4);
}

.elevation-5 {
  box-shadow: var(--md-sys-elevation-level5);
}

/* Shape Utilities */
/* .shape-none {
  border-radius: var(--md-sys-shape-corner-none);
}

.shape-extra-small {
  border-radius: var(--md-sys-shape-corner-extra-small);
}

.shape-small {
  border-radius: var(--md-sys-shape-corner-small);
}

.shape-medium {
  border-radius: var(--md-sys-shape-corner-medium);
}

.shape-large {
  border-radius: var(--md-sys-shape-corner-large);
}

.shape-extra-large {
  border-radius: var(--md-sys-shape-corner-extra-large);
}

.shape-full {
  border-radius: var(--md-sys-shape-corner-full);
} */

/* Spacing Utilities */
.p-0 {
  padding: var(--md-sys-spacing-0);
}
.p-1 {
  padding: var(--md-sys-spacing-1);
}
.p-2 {
  padding: var(--md-sys-spacing-2);
}
.p-3 {
  padding: var(--md-sys-spacing-3);
}
.p-4 {
  padding: var(--md-sys-spacing-4);
}
.p-5 {
  padding: var(--md-sys-spacing-5);
}
.p-6 {
  padding: var(--md-sys-spacing-6);
}
.p-8 {
  padding: var(--md-sys-spacing-8);
}

.m-0 {
  margin: var(--md-sys-spacing-0);
}
.m-1 {
  margin: var(--md-sys-spacing-1);
}
.m-2 {
  margin: var(--md-sys-spacing-2);
}
.m-3 {
  margin: var(--md-sys-spacing-3);
}
.m-4 {
  margin: var(--md-sys-spacing-4);
}
.m-5 {
  margin: var(--md-sys-spacing-5);
}
.m-6 {
  margin: var(--md-sys-spacing-6);
}
.m-8 {
  margin: var(--md-sys-spacing-8);
}

/* Responsive Utilities */
@media (max-width: 600px) {
  .container {
    padding: 0 var(--md-sys-spacing-2);
  }

  .hide-on-compact {
    display: none;
  }
}

@media (min-width: 601px) and (max-width: 840px) {
  .hide-on-medium {
    display: none;
  }
}

@media (min-width: 841px) {
  .hide-on-expanded {
    display: none;
  }
}

/* Focus and State Utilities */
.focus-ring {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

.disabled {
  opacity: 0.38;
  pointer-events: none;
}

/* Animation Utilities */
.transition-standard {
  transition: all var(--md-sys-motion-duration-medium2)
    var(--md-sys-motion-easing-standard);
}

.transition-emphasized {
  transition: all var(--md-sys-motion-duration-medium4)
    var(--md-sys-motion-easing-emphasized);
}

/* Page Placeholder Styles */
.page-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-6);
  min-height: calc(100vh - 64px); /* Full height minus top app bar */
  padding: var(--md-sys-spacing-8) var(--md-sys-spacing-4);
  text-align: center;
  background-color: var(--md-sys-color-surface-container-low);
  width: 100%;
}

.page-placeholder h1 {
  margin: 0;
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
}

.page-placeholder p {
  margin: 0;
  color: var(--md-sys-color-on-surface-variant);
  max-width: 500px;
}

/* Removed global md-filled-button styles to allow default Material Design appearance */

/* See More 按钮使用官方 md-elevated-button 组件，样式在各自的CSS文件中定义 */

/* Protected Route Loading Styles */
.protected-route-loading {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background: var(--md-sys-color-surface);
}

.protected-route-loading p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}
