declare module '@heroicons/react/24/solid' {
  import { ComponentType, SVGProps } from 'react';
  
  export const PauseCircleIcon: ComponentType<SVGProps<SVGSVGElement>>;
  export const PlayCircleIcon: ComponentType<SVGProps<SVGSVGElement>>;
  export const ForwardIcon: ComponentType<SVGProps<SVGSVGElement>>;
  export const BackwardIcon: ComponentType<SVGProps<SVGSVGElement>>;
  export const ArrowPathIcon: ComponentType<SVGProps<SVGSVGElement>>;
  export const ArrowsRightLeftIcon: ComponentType<SVGProps<SVGSVGElement>>;
  export const HeartIcon: ComponentType<SVGProps<SVGSVGElement>>;
}

declare module '@heroicons/react/24/outline' {
  import { ComponentType, SVGProps } from 'react';
  
  export const HeartIcon: ComponentType<SVGProps<SVGSVGElement>>;
}
